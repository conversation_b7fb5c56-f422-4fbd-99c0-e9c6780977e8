<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能书签管理器</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    时间轴书签
                </h1>
                <div class="header-actions">
                    <div class="scrollbar-settings-container">
                        <button class="scrollbar-settings" id="scrollbarSettings" title="滚动条设置">
                            <svg class="scrollbar-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                            </svg>
                        </button>
                        
                        <!-- 设置面板 -->
                        <div class="scrollbar-panel" id="scrollbarPanel">
                            <div class="panel-title">底部按钮颜色：</div>
                            <div class="color-options">
                                <div class="color-group">
                                    <div class="color-presets button-colors">
                                        <button class="color-preset button-color-preset" data-color="red" style="background: linear-gradient(45deg, #ef4444, #dc2626)" title="赤"></button>
                                        <button class="color-preset button-color-preset" data-color="orange" style="background: linear-gradient(45deg, #f97316, #ea580c)" title="橙"></button>
                                        <button class="color-preset button-color-preset" data-color="yellow" style="background: linear-gradient(45deg, #eab308, #ca8a04)" title="黄"></button>
                                        <button class="color-preset button-color-preset" data-color="green" style="background: linear-gradient(45deg, #22c55e, #16a34a)" title="绿"></button>
                                        <button class="color-preset button-color-preset" data-color="cyan" style="background: linear-gradient(45deg, #06b6d4, #0891b2)" title="青"></button>
                                        <button class="color-preset button-color-preset" data-color="blue" style="background: linear-gradient(45deg, #3b82f6, #2563eb)" title="蓝"></button>
                                        <button class="color-preset button-color-preset" data-color="purple" style="background: linear-gradient(45deg, #8b5cf6, #7c3aed)" title="紫"></button>
                                    </div>
                                </div>
                                <div class="panel-title">滚动条颜色：</div>
                                <div class="color-group">
                                    <div class="color-presets scrollbar-colors">
                                        <button class="color-preset scrollbar-color-preset" data-color="red" style="background: linear-gradient(45deg, #ef4444, #dc2626)" title="赤"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="orange" style="background: linear-gradient(45deg, #f97316, #ea580c)" title="橙"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="yellow" style="background: linear-gradient(45deg, #eab308, #ca8a04)" title="黄"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="green" style="background: linear-gradient(45deg, #22c55e, #16a34a)" title="绿"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="cyan" style="background: linear-gradient(45deg, #06b6d4, #0891b2)" title="青"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="blue" style="background: linear-gradient(45deg, #3b82f6, #2563eb)" title="蓝"></button>
                                        <button class="color-preset scrollbar-color-preset" data-color="purple" style="background: linear-gradient(45deg, #8b5cf6, #7c3aed)" title="紫"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="切换主题">
                        <svg class="theme-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="search-container" id="searchContainer">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索书签...">
                <button class="search-clear" id="searchClear">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
            
        </header>

        <!-- 书签列表区域 -->
        <main class="main-content">
            <!-- 加载骨架屏 -->
            <div class="skeleton-container" id="skeletonContainer">
                <div class="skeleton-item" v-for="i in 5">
                    <div class="skeleton-icon"></div>
                    <div class="skeleton-content">
                        <div class="skeleton-title"></div>
                        <div class="skeleton-date"></div>
                    </div>
                </div>
            </div>

            <!-- 书签列表 -->
            <div class="bookmarks-container" id="bookmarksContainer">
                <div class="bookmarks-list" id="bookmarksList"></div>
                
                <!-- 加载更多指示器 -->
                <div class="loading-indicator" id="loadingIndicator">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>
                
                <!-- 无更多数据提示 -->
                <div class="no-more-data" id="noMoreData">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                    </svg>
                    <span>已显示全部书签</span>
                </div>
                
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3M17,18L12,15.82L7,18V5H17V18Z"/>
                    </svg>
                    <h3>暂无书签</h3>
                    <p>开始收藏您喜欢的网页吧</p>
                </div>
            </div>
        </main>

        <!-- 底部操作区域 -->
        <footer class="footer">
            <button class="manager-button" id="managerButton">
                <img class="manager-icon" src="icons/folder.png" alt="文件夹图标" width="18" height="18">
                打开书签管理器
            </button>
            
            <!-- 添加书签按钮 -->
            <button class="add-bookmark-button" id="addBookmarkButton">
                <svg class="add-bookmark-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17,3H7A2,2 0 0,0 5,5V21L12,18L19,21V5C19,3.89 18.1,3 17,3M12,7A2,2 0 0,1 14,9A2,2 0 0,1 12,11A2,2 0 0,1 10,9A2,2 0 0,1 12,7M16,15H8V14C8,12.67 10.67,12 12,12C13.33,12 16,12.67 16,14V15Z"/>
                </svg>
                <span class="add-bookmark-text">添加当前页面</span>
            </button>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>