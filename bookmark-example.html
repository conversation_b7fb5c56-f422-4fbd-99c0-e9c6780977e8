<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书签项示例</title>
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f8fafc;
            color: #1a202c;
            padding: 20px;
        }
        
        /* 书签项样式 */
        .bookmark-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background-color: #ffffff;
            cursor: pointer;
            transition: background-color 0.2s ease;
            position: relative;
            overflow: hidden;
            width: 380px;
            border-bottom: 1px solid rgba(203, 213, 225, 0.6);
        }
        
        .bookmark-item:hover {
            background-color: #f5f5f5;
        }
        
        .bookmark-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            flex-shrink: 0;
            background: #ffffff;
            border: 1px solid rgba(203, 213, 225, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        
        .bookmark-icon.immersive {
            background-color: #ff4e8b;
            border-color: #ff4e8b;
        }
        
        .bookmark-icon .site-initial {
            color: #fff;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .bookmark-content {
            flex: 1;
            min-width: 0;
        }
        
        .bookmark-title {
            font-size: 14px;
            font-weight: 500;
            color: #1a202c;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
            margin-bottom: 2px;
        }
        
        .bookmark-meta {
            font-size: 11px;
            color: #718096;
            line-height: 1.1;
        }
        
        .bookmark-url, .bookmark-date {
            display: inline;
        }
        
        .bookmark-url {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .bookmark-separator {
            display: inline-block;
            width: 3px;
            height: 3px;
            background-color: #718096;
            border-radius: 50%;
            margin: 0 4px;
            vertical-align: middle;
        }
        
        .bookmark-date {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <h2>书签项示例</h2>
    
    <!-- 示例1：短URL -->
    <div class="bookmark-item">
        <div class="bookmark-icon immersive">
            <span class="site-initial">翻</span>
        </div>
        <div class="bookmark-content">
            <div class="bookmark-title">沉浸式翻译 - 文本翻译：一键翻译网页</div>
            <div class="bookmark-meta">
                <span class="bookmark-url">app.immersivetranslate.com</span>
                <span class="bookmark-separator">•</span>
                <span class="bookmark-date">13 分钟前</span>
            </div>
        </div>
    </div>
    
    <br>
    
    <!-- 示例2：长URL -->
    <div class="bookmark-item">
        <div class="bookmark-icon immersive">
            <span class="site-initial">B</span>
        </div>
        <div class="bookmark-content">
            <div class="bookmark-title">百度百科 - 全球最大中文百科全书</div>
            <div class="bookmark-meta">
                <span class="bookmark-url">baike.baidu.com/item/百度百科/1785</span>
                <span class="bookmark-separator">•</span>
                <span class="bookmark-date">2 小时前</span>
            </div>
        </div>
    </div>
    
    <br>
    
    <!-- 示例3：超长URL -->
    <div class="bookmark-item">
        <div class="bookmark-icon immersive">
            <span class="site-initial">G</span>
        </div>
        <div class="bookmark-content">
            <div class="bookmark-title">Google搜索结果</div>
            <div class="bookmark-meta">
                <span class="bookmark-url">google.com/search?q=如何在CSS中实现文本溢出时显示省略号并且保持在同一行显示</span>
                <span class="bookmark-separator">•</span>
                <span class="bookmark-date">1 天前</span>
            </div>
        </div>
    </div>
</body>
</html>