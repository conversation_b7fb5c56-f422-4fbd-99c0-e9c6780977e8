/**
 * 这是一个示例代码片段，展示如何生成符合新CSS结构的书签项HTML
 * 您需要将这段逻辑整合到您的popup.js文件中
 */

// 创建书签项的函数
function createBookmarkItem(bookmark) {
  // 创建书签项容器
  const bookmarkItem = document.createElement('div');
  bookmarkItem.className = 'bookmark-item';
  bookmarkItem.dataset.id = bookmark.id;
  
  // 创建图标
  const bookmarkIcon = document.createElement('div');
  bookmarkIcon.className = 'bookmark-icon';
  
  // 根据网站类型添加特定类名
  if (bookmark.url.includes('immersive') || bookmark.url.includes('translate')) {
    bookmarkIcon.classList.add('immersive');
  } else if (bookmark.url.includes('baidu.com')) {
    bookmarkIcon.classList.add('baidu');
  } else if (bookmark.url.includes('linux')) {
    bookmarkIcon.classList.add('linux');
  }
  
  // 添加图标图片或初始字母
  if (bookmark.favicon) {
    const img = document.createElement('img');
    img.src = bookmark.favicon;
    img.alt = '网站图标';
    bookmarkIcon.appendChild(img);
  } else {
    // 如果没有图标，显示网站首字母
    const initial = document.createElement('span');
    initial.className = 'site-initial';
    initial.textContent = getDomainInitial(bookmark.url);
    bookmarkIcon.appendChild(initial);
  }
  
  // 创建内容区域
  const bookmarkContent = document.createElement('div');
  bookmarkContent.className = 'bookmark-content';
  
  // 添加标题
  const bookmarkTitle = document.createElement('div');
  bookmarkTitle.className = 'bookmark-title';
  bookmarkTitle.textContent = bookmark.title;
  bookmarkContent.appendChild(bookmarkTitle);
  
  // 创建元数据区域（URL和日期）
  const bookmarkMeta = document.createElement('div');
  bookmarkMeta.className = 'bookmark-meta';
  
  // 添加URL
  const bookmarkUrl = document.createElement('span');
  bookmarkUrl.className = 'bookmark-url';
  bookmarkUrl.textContent = getDomain(bookmark.url);
  bookmarkMeta.appendChild(bookmarkUrl);
  
  // 添加分隔点
  const separator = document.createElement('span');
  separator.className = 'bookmark-separator';
  separator.textContent = '•';
  bookmarkMeta.appendChild(separator);
  
  // 添加日期
  const bookmarkDate = document.createElement('span');
  bookmarkDate.className = 'bookmark-date';
  bookmarkDate.textContent = getRelativeTime(bookmark.dateAdded);
  bookmarkMeta.appendChild(bookmarkDate);
  
  // 将元数据添加到内容区域
  bookmarkContent.appendChild(bookmarkMeta);
  
  // 组装书签项
  bookmarkItem.appendChild(bookmarkIcon);
  bookmarkItem.appendChild(bookmarkContent);
  
  // 添加点击事件
  bookmarkItem.addEventListener('click', () => {
    window.open(bookmark.url, '_blank');
  });
  
  return bookmarkItem;
}

// 辅助函数：获取域名首字母
function getDomainInitial(url) {
  try {
    const domain = new URL(url).hostname;
    return domain.charAt(0).toUpperCase();
  } catch (e) {
    return 'B'; // 默认为B (Bookmark)
  }
}

// 辅助函数：获取域名
function getDomain(url) {
  try {
    return new URL(url).hostname;
  } catch (e) {
    return url;
  }
}

// 辅助函数：获取相对时间
function getRelativeTime(timestamp) {
  const now = new Date();
  const date = new Date(timestamp);
  const diff = Math.floor((now - date) / 1000 / 60); // 分钟差
  
  if (diff < 1) {
    return '刚刚';
  } else if (diff < 60) {
    return `${diff} 分钟前`;
  } else if (diff < 1440) {
    return `${Math.floor(diff / 60)} 小时前`;
  } else {
    return `${Math.floor(diff / 1440)} 天前`;
  }
}

// 示例用法
const bookmarkExample = {
  id: '123',
  title: '沉浸式翻译 - 文本翻译：一键翻译网页',
  url: 'https://app.immersivetranslate.com/',
  favicon: 'https://app.immersivetranslate.com/favicon.ico',
  dateAdded: Date.now() - 13 * 60 * 1000 // 13分钟前
};

// 将示例书签项添加到页面
document.addEventListener('DOMContentLoaded', () => {
  const bookmarksList = document.getElementById('bookmarksList');
  if (bookmarksList) {
    const bookmarkItem = createBookmarkItem(bookmarkExample);
    bookmarksList.appendChild(bookmarkItem);
  }
});