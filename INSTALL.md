# 智能书签管理器 - 安装指南

## 📋 安装前准备

### 系统要求
- Chrome浏览器版本 88+ 
- 支持Manifest V3的Chrome版本
- 启用开发者模式权限

### 文件检查
确保项目包含以下文件：
- ✅ `manifest.json` - 扩展配置文件
- ✅ `popup.html` - 弹窗页面
- ✅ `popup.css` - 样式文件  
- ✅ `popup.js` - 主要逻辑
- ✅ `background.js` - 后台脚本
- ✅ `welcome.html` - 欢迎页面
- ⚠️ `icons/` 文件夹（需要手动添加图标）

## 🚀 详细安装步骤

### 步骤1：准备图标文件

由于无法自动生成图像文件，您需要手动添加图标：

1. **下载或创建图标**
   - 访问 https://icons8.com/ 搜索"bookmark"
   - 或使用 https://favicon.io/ 生成图标
   - 需要16x16、32x32、48x48、128x128四种尺寸

2. **放置图标文件**
   ```
   icons/
   ├── icon16.png
   ├── icon32.png  
   ├── icon48.png
   └── icon128.png
   ```

### 步骤2：打开Chrome扩展管理页面

**方法一：地址栏输入**
```
chrome://extensions/
```

**方法二：通过菜单**
1. 点击Chrome右上角三点菜单
2. 选择"更多工具" → "扩展程序"

### 步骤3：启用开发者模式

1. 在扩展管理页面右上角
2. 找到"开发者模式"开关
3. 点击开启（变为蓝色状态）

### 步骤4：加载扩展

1. 点击"加载已解压的扩展程序"按钮
2. 在文件选择器中导航到项目文件夹
3. 选择包含`manifest.json`的根目录
4. 点击"选择文件夹"

### 步骤5：验证安装

安装成功后您应该看到：
- ✅ 扩展出现在扩展列表中
- ✅ 浏览器工具栏出现书签图标
- ✅ 自动打开欢迎页面
- ✅ 扩展状态显示为"已启用"

## 🔧 安装后配置

### 固定扩展图标
1. 点击浏览器工具栏右侧的拼图图标
2. 找到"智能书签管理器"
3. 点击图钉图标固定到工具栏

### 设置权限
扩展会自动请求以下权限：
- 📚 **书签权限** - 读取和管理书签
- 💾 **存储权限** - 保存用户设置
- 🔗 **标签页权限** - 打开新标签页

### 初始设置
1. 点击扩展图标打开popup
2. 选择您喜欢的主题（深色/浅色）
3. 测试搜索功能
4. 尝试滚动加载更多书签

## ❗ 常见安装问题

### 问题1：扩展无法加载
**症状**：点击"加载已解压的扩展程序"后没有反应

**解决方案**：
- 确保选择的是包含`manifest.json`的根目录
- 检查`manifest.json`语法是否正确
- 确保所有必需文件都存在

### 问题2：图标不显示
**症状**：扩展加载成功但图标显示为默认图标

**解决方案**：
- 检查`icons/`文件夹是否存在
- 确保图标文件名与`manifest.json`中的配置一致
- 图标文件格式应为PNG或JPG

### 问题3：权限被拒绝
**症状**：扩展无法访问书签数据

**解决方案**：
- 检查`manifest.json`中的permissions配置
- 重新加载扩展
- 确保Chrome版本支持Manifest V3

### 问题4：popup页面空白
**症状**：点击扩展图标后弹出空白页面

**解决方案**：
- 右键点击扩展图标选择"检查弹出内容"
- 查看控制台是否有JavaScript错误
- 检查`popup.html`、`popup.css`、`popup.js`文件是否完整

## 🔄 更新扩展

### 开发模式更新
1. 修改代码后保存文件
2. 在扩展管理页面点击刷新图标
3. 或者点击"重新加载"按钮

### 完全重新安装
1. 在扩展管理页面点击"移除"
2. 重新执行安装步骤

## 🧪 测试功能

安装完成后，建议测试以下功能：

### 基础功能测试
- [ ] 点击扩展图标能打开popup
- [ ] 能看到现有书签列表
- [ ] 点击书签能正常打开网页
- [ ] 滚动能加载更多书签

### 搜索功能测试  
- [ ] 点击搜索图标能展开搜索框
- [ ] 输入关键词能实时搜索
- [ ] 搜索结果正确高亮显示
- [ ] 清除搜索能恢复完整列表

### 主题功能测试
- [ ] 点击主题切换按钮能切换主题
- [ ] 主题设置能持久保存
- [ ] 深色和浅色主题显示正常

### 管理功能测试
- [ ] 点击"打开书签管理器"能跳转到Chrome书签页面
- [ ] 右键菜单功能正常（如果实现了）

## 📞 获取帮助

如果遇到安装问题：

1. **检查控制台错误**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **查看扩展错误**
   - 在扩展管理页面点击"错误"按钮
   - 查看详细错误信息

3. **重置扩展**
   - 移除扩展后重新安装
   - 清除浏览器缓存

4. **联系支持**
   - 查看README.md中的联系方式
   - 在GitHub提交Issue

---

**祝您使用愉快！** 🎉