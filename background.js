// Chrome扩展后台脚本
// 使用Manifest V3的Service Worker

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('智能书签管理器已安装');
  
  // 设置默认配置
  chrome.storage.sync.set({
    theme: 'light',
    pageSize: 20,
    autoLoad: true
  });
  
  // 如果是首次安装，显示欢迎页面
  if (details.reason === 'install') {
    chrome.tabs.create({
      url: chrome.runtime.getURL('welcome.html')
    });
  }
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
  // 由于我们使用popup，这个事件通常不会触发
  // 但保留作为备用方案
  console.log('扩展图标被点击');
});

// 监听书签变化事件
chrome.bookmarks.onCreated.addListener((id, bookmark) => {
  console.log('新书签已创建:', bookmark.title);
  
  // 可以在这里添加通知功能
  if (bookmark.url) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: '新书签已添加',
      message: `${bookmark.title} 已添加到书签`
    });
  }
});

chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
  console.log('书签已删除:', id);
});

chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
  console.log('书签已更新:', changeInfo);
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'getBookmarks':
      handleGetBookmarks(request, sendResponse);
      return true; // 保持消息通道开放

    case 'openBookmarkManager':
      chrome.tabs.create({ url: 'chrome://bookmarks/' });
      sendResponse({ success: true });
      break;

    case 'updateTheme':
      chrome.storage.sync.set({ theme: request.theme });
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ error: '未知操作' });
  }
});

// 获取书签数据的处理函数
async function handleGetBookmarks(request, sendResponse) {
  try {
    const bookmarkTree = await chrome.bookmarks.getTree();
    const bookmarks = extractBookmarksFromTree(bookmarkTree);

    // 按日期降序排序
    bookmarks.sort((a, b) => b.dateAdded - a.dateAdded);

    sendResponse({
      success: true,
      bookmarks: bookmarks,
      total: bookmarks.length
    });
  } catch (error) {
    console.error('获取书签失败:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// 从书签树中提取所有书签
function extractBookmarksFromTree(nodes, bookmarks = []) {
  for (const node of nodes) {
    if (node.url) {
      bookmarks.push({
        id: node.id,
        title: node.title || '未命名书签',
        url: node.url,
        dateAdded: node.dateAdded || Date.now(),
        parentId: node.parentId,
        index: node.index
      });
    }
    
    if (node.children) {
      extractBookmarksFromTree(node.children, bookmarks);
    }
  }
  
  return bookmarks;
}

// 清理存储数据（可选功能）
chrome.runtime.onSuspend.addListener(() => {
  console.log('扩展即将被挂起，清理资源');
  // 在这里可以添加清理逻辑
});

// 处理扩展更新
chrome.runtime.onUpdateAvailable.addListener((details) => {
  console.log('扩展更新可用:', details.version);
  // 可以选择立即重启以应用更新
  // chrome.runtime.reload();
});

// 错误处理
chrome.runtime.onStartup.addListener(() => {
  console.log('扩展启动');
});

// 监听标签页更新，用于可能的书签同步
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 可以在这里添加自动书签建议功能
    // 检查当前页面是否已被收藏等
  }
});

// 上下文菜单（右键菜单）功能
chrome.runtime.onInstalled.addListener(() => {
  // 创建右键菜单项
  chrome.contextMenus.create({
    id: 'addToBookmarks',
    title: '添加到智能书签',
    contexts: ['page', 'link']
  });
  
  chrome.contextMenus.create({
    id: 'openBookmarkManager',
    title: '打开书签管理器',
    contexts: ['page']
  });
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    case 'addToBookmarks':
      const url = info.linkUrl || tab.url;
      const title = info.linkUrl ? info.linkText : tab.title;
      
      chrome.bookmarks.create({
        title: title,
        url: url
      }, (bookmark) => {
        if (chrome.runtime.lastError) {
          console.error('添加书签失败:', chrome.runtime.lastError);
        } else {
          console.log('书签添加成功:', bookmark);
        }
      });
      break;
      
    case 'openBookmarkManager':
      chrome.tabs.create({ url: 'chrome://bookmarks/' });
      break;
  }
});

// 键盘快捷键支持
chrome.commands.onCommand.addListener((command) => {
  switch (command) {
    case 'open-bookmark-manager':
      chrome.tabs.create({ url: 'chrome://bookmarks/' });
      break;
      
    case 'toggle-popup':
      // 打开popup（如果支持的话）
      chrome.action.openPopup();
      break;
  }
});

// 存储变化监听
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log('存储数据变化:', changes, namespace);
  
  // 可以在这里处理主题变化等事件
  if (changes.theme) {
    console.log('主题已更改为:', changes.theme.newValue);
  }
});

// 网络状态监听（用于离线功能）
chrome.runtime.onConnect.addListener((port) => {
  console.log('建立连接:', port.name);
  
  port.onMessage.addListener((msg) => {
    console.log('收到消息:', msg);
    
    // 处理来自popup的实时消息
    switch (msg.type) {
      case 'ping':
        port.postMessage({ type: 'pong' });
        break;
        
      case 'getStatus':
        port.postMessage({
          type: 'status',
          online: navigator.onLine,
          timestamp: Date.now()
        });
        break;
    }
  });
  
  port.onDisconnect.addListener(() => {
    console.log('连接已断开');
  });
});

// 定期清理缓存数据（可选）
chrome.alarms.create('cleanupCache', { periodInMinutes: 60 });

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'cleanupCache') {
    console.log('执行缓存清理');
    // 清理过期的缓存数据
    chrome.storage.local.clear();
  }
});

// 扩展卸载时的清理
chrome.runtime.setUninstallURL('https://forms.gle/feedback', () => {
  console.log('卸载URL已设置');
});