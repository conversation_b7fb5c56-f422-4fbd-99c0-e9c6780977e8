<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用智能书签管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            max-width: 600px;
            padding: 40px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 32px;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .subtitle {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .feature h3 {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .feature p {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.5;
        }

        .actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .version {
            margin-top: 32px;
            font-size: 12px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📚</div>
        <h1>智能书签管理器</h1>
        <p class="subtitle">现代化的Chrome书签管理扩展，让您的书签管理更加高效便捷</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3>快速访问</h3>
                <p>点击扩展图标即可快速查看最新书签，支持分页加载和实时搜索</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3>现代化界面</h3>
                <p>采用glassmorphism设计风格，支持深色/浅色主题切换</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <h3>智能搜索</h3>
                <p>支持书签标题和URL搜索，快速找到您需要的书签</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>响应式设计</h3>
                <p>完美适配不同屏幕尺寸，提供流畅的用户体验</p>
            </div>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="openBookmarkManager()">
                <span>📖</span>
                打开书签管理器
            </button>
            <button class="btn btn-secondary" onclick="window.close()">
                <span>✨</span>
                开始使用
            </button>
        </div>
        
        <div class="version">
            版本 1.0.0 | 感谢您的使用
        </div>
    </div>

    <script>
        function openBookmarkManager() {
            chrome.tabs.create({ url: 'chrome://bookmarks/' });
        }

        // 添加一些交互动画
        document.addEventListener('DOMContentLoaded', () => {
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>