/* CSS变量定义 */
:root {
  /* 浅色主题 */
  --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --bg-secondary: rgba(255, 255, 255, 1);
  --bg-glass: rgba(255, 255, 255, 0.9);
  --bg-hover: rgba(255, 255, 255, 0.95);
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-color: rgba(203, 213, 225, 0.6);
  --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
  --accent-color: #667eea;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --scrollbar-track: rgba(0, 0, 0, 0.05);
  --scrollbar-thumb: rgba(0, 0, 0, 0.2);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
  --button-color: #667eea;
  /* --header-gradient: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); */
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  --bg-secondary: rgba(26, 32, 44, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-hover: rgba(255, 255, 255, 0.1);
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);
  --accent-color: #63b3ed;
  --scrollbar-track: rgba(255, 255, 255, 0.05);
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 容器样式 */
.container {
  width: 380px;
  height: 600px;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(20px);
  border: none;
  box-shadow: none;
  overflow: hidden;
}

/* 头部样式 */
.header {
  background: var(--header-gradient);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 16px 8px 16px;
  flex-shrink: 0;
  position: relative;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.title-icon {
  width: 20px;
  height: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.theme-toggle,
.search-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.theme-toggle:hover,
.search-toggle:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.theme-icon,
.search-icon {
  width: 16px;
  height: 16px;
}

/* 搜索容器 */
.search-container {
  position: relative;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
  pointer-events: all;
}

.search-input {
  width: 100%;
  height: 40px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 0 40px 0 16px;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-clear {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  color: var(--text-muted);
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
}

.search-clear.visible {
  opacity: 1;
  visibility: visible;
}

.search-clear:hover {
  color: var(--text-secondary);
}

.search-clear svg {
  width: 16px;
  height: 16px;
}

/* 滚动条设置容器 */
.scrollbar-settings-container {
  position: relative;
}

/* 滚动条设置按钮 */
.scrollbar-settings {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.scrollbar-settings:hover {
  background: var(--bg-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.scrollbar-icon {
  width: 16px;
  height: 16px;
}

/* 滚动条设置面板 */
.scrollbar-panel {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 240px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 9999;
}

.scrollbar-panel.active {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.panel-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.color-group {
  margin-bottom: 8px;
}

.color-group label {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  display: block;
}

.color-presets {
  display: flex;
  gap: 3px;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.color-preset {
  width: 24px;
  height: 24px;
  border: 2px solid transparent;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.color-preset:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.color-preset.active {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.color-preset::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.color-preset.active::after {
  opacity: 1;
}


/* 主内容区域 */
.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 12px 16px;
}

.skeleton-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  margin-bottom: 10px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  box-shadow: var(--shadow-light);
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-icon {
  width: 32px;
  height: 32px;
  background: #e2e8f0;
  border-radius: 8px;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  position: relative;
  min-height: 50px;
}

.skeleton-title {
  height: 16px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 80%;
}

.skeleton-date {
  height: 12px;
  background: #f1f5f9;
  border-radius: 4px;
  width: 40%;
  position: absolute;
  right: 0;
  bottom: 0;
}

@keyframes skeleton-loading {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 书签容器 */
.bookmarks-container {
  height: 100%;
  overflow-y: auto;
  padding: 0;
  margin: 0;
}

/* 自定义滚动条 */
.bookmarks-container::-webkit-scrollbar {
  width: 6px;
}

.bookmarks-container::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 3px;
}

.bookmarks-container::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 3px;
}

.bookmarks-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 书签列表 */
.bookmarks-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin: 0;
  padding: 0;
}

/* 日期分组样式 */
.date-group {
  margin: 0;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  margin: 0;
  background: #f8f9fa;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  position: static;
  width: 100%;
  border: none;
}

[data-theme="dark"] .group-title {
  background: #2d3748;
}

.group-icon {
  width: 12px;
  height: 12px;
  color: var(--accent-color);
  flex-shrink: 0;
}

.group-bookmarks {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0;
  margin: 0;
}

/* 书签项样式 - 简洁布局 */
.bookmark-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  background: #ffffff;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  overflow: hidden;
  width: 100%;
  border: none;
  margin: 0;
}

.bookmark-item:hover {
  background: #f5f5f5;
}

[data-theme="dark"] .bookmark-item {
  background-color: #1a202c;
}

[data-theme="dark"] .bookmark-item:hover {
  background-color: #2d3748;
}

/* 图标外层包装器 - 灰色圆角矩形背景 */
.bookmark-icon-wrapper {
  width: 28px;
  height: 28px;
  background: #f0f0f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

[data-theme="dark"] .bookmark-icon-wrapper {
  background: #4a5568;
}

.bookmark-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.bookmark-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 特定网站图标样式 */
.bookmark-icon.immersive {
  background-color: #ff4e8b;
  border-color: #ff4e8b;
}

.bookmark-icon.baidu {
  background-color: #4e6ef2;
  border-color: #4e6ef2;
}

.bookmark-icon.linux {
  background-color: #333;
  border-color: #333;
}

/* 网站图标内部的文字样式 */
.bookmark-icon .site-initial {
  color: #fff;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.bookmark-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  margin-bottom: 2px;
}

.bookmark-meta {
  font-size: 11px;
  color: var(--text-muted);
  line-height: 1.1;
}

.bookmark-url, .bookmark-date {
  display: inline;
}

.bookmark-url {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-separator {
  display: inline-block;
  width: 3px;
  height: 3px;
  background-color: var(--text-muted);
  border-radius: 50%;
  margin: 0 4px;
  vertical-align: middle;
}

.bookmark-date {
  white-space: nowrap;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 15px;
  color: var(--text-secondary);
  font-size: 14px;
  background: transparent;
  border-bottom: 1px solid var(--border-color);
  margin: 0;
  width: 100%;
}

.loading-spinner {
  width: 22px;
  height: 22px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无更多数据提示 */
.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 15px;
  color: var(--text-secondary);
  font-size: 14px;
  background: transparent;
  border-bottom: 1px solid var(--border-color);
  margin: 0;
  width: 100%;
}

.no-more-data svg {
  width: 18px;
  height: 18px;
  color: var(--success-color);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--text-muted);
  background: transparent;
  margin: 0;
  padding: 20px;
  width: 100%;
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.7;
  color: var(--text-secondary);
}

.empty-state h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 600;
}

.empty-state p {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 底部操作区域 */
.footer {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--border-color);
  padding: 16px;
  flex-shrink: 0;
}

.manager-button {
  width: 100%;
  height: 44px;
  background: var(--button-color);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.manager-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.manager-button:hover::before {
  left: 100%;
}

.manager-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  filter: brightness(1.1);
}

.manager-button:active {
  transform: translateY(0);
}

.manager-icon {
  width: 18px;
  height: 18px;
}

/* 快捷键提示样式 */
.shortcut-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 12px;
  color: var(--text-muted);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.shortcut-tip:hover {
  background: var(--bg-hover);
  color: var(--text-secondary);
  transform: translateY(-1px);
}

.shortcut-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
  opacity: 0.7;
}

.shortcut-text {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.key {
  display: inline-block;
  padding: 2px 6px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 10px;
  font-weight: 600;
  color: var(--text-primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 20px;
  text-align: center;
  line-height: 1.2;
}

.shortcut-desc {
  margin-left: 4px;
  font-size: 11px;
  opacity: 0.8;
}

/* 深色主题下的快捷键样式调整 */
[data-theme="dark"] .key {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 隐藏状态 */
.hidden {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .container {
    width: 320px;
  }
  
  .bookmark-item {
    padding: 10px;
  }
  
  .bookmark-icon {
    width: 16px;
    height: 16px;
  }
  
  .bookmark-title {
    font-size: 13px;
  }
  
  .bookmark-date {
    font-size: 11px;
  }
}

/* 简单的动画效果 */
.bookmark-item {
  opacity: 1;
}

/* 搜索结果高亮 */
.bookmark-title .highlight {
  background: var(--accent-color);
  color: white;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
}